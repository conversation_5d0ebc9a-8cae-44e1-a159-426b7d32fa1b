import json
import logging
import os
import sys
import time
import tensorflow as tf
import multiprocessing
from revamped_new import TSNGCLEnvironment, create_ppo_agent, TSNGCLTrainer
from tf_agents.trajectories import time_step as ts

# use WRAPT_DISABLE_EXTENSIONS=true before running this code

# Configure multiprocessing to use spawn method which is more stable during cleanup
if sys.platform != 'win32':  # Not needed on Windows
    multiprocessing.set_start_method('spawn', force=True)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    filename='best_agent.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a'
)

def load_best_params(file_path="best_params_improved.txt"):
    """Load the best parameters from the file."""
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found. Run Optuna optimization first.")
        logging.error(f"Error: {file_path} not found. Run Optuna optimization first.")
        return None

    # Parse the best parameters file
    best_params = {}
    with open(file_path, 'r') as f:
        lines = f.readlines()
        for line in lines[2:]:  # Skip the first two lines (trial number and value)
            if ':' in line:
                key, value = line.strip().split(':', 1)
                key = key.strip()
                value = value.strip()

                # Convert value to appropriate type
                if key == 'n_layers' or key == 'num_epochs':
                    best_params[key] = int(value)
                elif key == 'layer_size':
                    best_params[key] = int(value)
                elif key == 'activation_fn':
                    best_params[key] = value
                else:
                    try:
                        best_params[key] = float(value)
                    except ValueError:
                        best_params[key] = value

    return best_params

def save_model(agent, env, iteration=None, best_params=None, returns=None):
    """Saves the agent's policy with proper TF-Agents serialization."""
    model_dir = "saved_models"
    os.makedirs(model_dir, exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    if isinstance(iteration, int):
        session_dir = os.path.join(model_dir, f"session_{timestamp}_iter_{iteration}")
    else:
        session_dir = os.path.join(model_dir, f"session_{timestamp}_{iteration}")

    os.makedirs(session_dir, exist_ok=True)

    try:
        # Instead of using PolicySaver, save the underlying networks directly
        # 1. Save the actor network
        actor_dir = os.path.join(session_dir, "actor_network")
        os.makedirs(actor_dir, exist_ok=True)

        # Get the actor network from the agent
        actor_network = agent._actor_net
        # Save the actor network using TensorFlow's SavedModel format
        tf.saved_model.save(actor_network, actor_dir)
        print(f"Actor network saved to {actor_dir}")

        # 2. Save the value network
        value_dir = os.path.join(session_dir, "value_network")
        os.makedirs(value_dir, exist_ok=True)

        # Get the value network from the agent
        value_network = agent._value_net
        # Save the value network using TensorFlow's SavedModel format
        tf.saved_model.save(value_network, value_dir)
        print(f"Value network saved to {value_dir}")

        # 3. Save additional training artifacts
        if best_params:
            with open(os.path.join(session_dir, "params.json"), 'w') as f:
                json.dump(best_params, f, indent=4)

        if returns:
            with open(os.path.join(session_dir, "returns.json"), 'w') as f:
                json.dump(returns, f, indent=4)

            try:
                import matplotlib.pyplot as plt
                plt.figure(figsize=(10, 6))
                plt.plot(returns)
                plt.title("Training Returns")
                plt.xlabel("Iteration")
                plt.ylabel("Return")
                plt.grid(True)
                plt.savefig(os.path.join(session_dir, "returns.png"))
                plt.close()
            except Exception as e:
                print(f"Couldn't save returns plot: {e}")

        # 4. Save the environment specs for future loading
        try:
            specs = {
                'observation_spec': str(agent.observation_spec()),
                'action_spec': str(agent.action_spec()),
                'time_step_spec': str(agent.time_step_spec())
            }
            with open(os.path.join(session_dir, "specs.json"), 'w') as f:
                json.dump(specs, f, indent=4)
        except Exception as e:
            print(f"Could not save specs: {e}")

        print(f"Model successfully saved to {session_dir}")
        return session_dir

    except Exception as e:
        print(f"Error saving model: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Run the best agent found by Optuna."""
    print("Starting best agent from Optuna optimization")
    print("NOTE: This agent will ONLY use real data from OMNeT++ and will pause when no data is available")
    print("      No synthetic data will be used during training or evaluation")
    print("      If loss explodes continuously, you'll be prompted to save and stop or reset and continue")
    logging.info("Starting best agent from Optuna optimization")
    logging.info("NOTE: This agent will ONLY use real data from OMNeT++ and will pause when no data is available")
    logging.info("      No synthetic data will be used during training or evaluation")
    logging.info("      If loss explodes continuously, user will be prompted to save and stop or reset and continue")

    # Load the best parameters
    best_params_file = input("Enter the path to the best parameters file (default: best_params_improved.txt): ") or "best_params_improved.txt"
    best_params = load_best_params(best_params_file)

    if best_params is None:
        return 1

    print(f"Loaded best parameters from {best_params_file}:")
    for key, value in best_params.items():
        print(f"  {key}: {value}")

    # Clean up any existing ZMQ context
    try:
        import zmq
        zmq.Context.instance().term()
        time.sleep(2)
        zmq._context_initialized = False
        print("Cleaned up existing ZMQ context")
        logging.info("Cleaned up existing ZMQ context")
    except Exception as e:
        print(f"Error cleaning up ZMQ context: {e}")
        logging.error(f"Error cleaning up ZMQ context: {e}")

    # Create environment with robust error handling
    max_attempts = 5
    env = None

    for attempt in range(max_attempts):
        try:
            print(f"Connection attempt {attempt+1}/{max_attempts}")
            logging.info(f"Connection attempt {attempt+1}/{max_attempts}")

            # Create environment
            env = TSNGCLEnvironment(port=5555)

            print(f"Connection attempt {attempt+1} successful")
            print("Environment initialized - waiting for real data from OMNeT++")
            print("No synthetic data will be used - agent will pause if no data is available")
            logging.info(f"Connection attempt {attempt+1} successful")
            logging.info("Environment initialized - waiting for real data from OMNeT++")
            logging.info("No synthetic data will be used - agent will pause if no data is available")

            # Wait for socket to initialize
            time.sleep(2)
            break
        except Exception as e:
            print(f"Error during connection attempt {attempt+1}: {e}")
            logging.error(f"Error during connection attempt {attempt+1}: {e}")
            if env:
                env.close()
            env = None
            time.sleep(5)

    if env is None:
        print("Failed to create environment after multiple attempts")
        logging.error("Failed to create environment after multiple attempts")
        return 1

    try:
        # Prepare parameters for agent creation
        learning_rate = best_params.get('learning_rate', 5e-5)

        # Create fc_layer_params based on n_layers and layer_size
        n_layers = best_params.get('n_layers', 2)
        layer_size = best_params.get('layer_size', 64)
        fc_layer_params = tuple([layer_size] * n_layers)

        # Map activation function
        activation_name = best_params.get('activation_fn', 'relu')
        activation_fn_map = {
            'relu': tf.keras.activations.relu,
            'elu': tf.keras.activations.elu,
            'tanh': tf.keras.activations.tanh,
            'swish': tf.nn.swish
        }
        activation_fn = activation_fn_map.get(activation_name, tf.keras.activations.relu)

        # Create the agent with best parameters
        agent, tf_env, lr_scheduler = create_ppo_agent(
            env,
            learning_rate=learning_rate,
            fc_layer_params=fc_layer_params,
            activation_fn=activation_fn,
            entropy_regularization=best_params.get('entropy_regularization', 0.01),
            importance_ratio_clipping=best_params.get('importance_ratio_clipping', 0.2),
            num_epochs=best_params.get('num_epochs', 3)
        )

        # Create trainer with more iterations for better training
        trainer = TSNGCLTrainer(
            env=env,
            agent=agent,
            tf_env=tf_env,
            lr_scheduler=lr_scheduler,
            num_iterations=50,  # More iterations for better training
            collect_steps_per_iteration=200  # More steps for better data collection
        )

        # Set early stopping parameters
        trainer.patience = 10  # More patience for better convergence
        trainer.min_delta_loss = 0.005  # Smaller delta for more precise convergence
        trainer.min_delta_return = 0.05  # Smaller delta for more precise convergence

        print("Starting training with best parameters...")
        print("Training will only use real data from OMNeT++ - agent will pause if no data is available")
        logging.info("Starting training with best parameters...")
        logging.info("Training will only use real data from OMNeT++ - agent will pause if no data is available")

        # Add a data activity check before starting training
        print("Checking for data activity...")
        data_check_start = time.time()
        data_check_timeout = 30  # seconds
        while not env.data_active and time.time() - data_check_start < data_check_timeout:
            print("Waiting for data from OMNeT++... (press Ctrl+C to skip check)")
            time.sleep(2)
            # The environment automatically monitors data activity in a background thread

        if env.data_active:
            print("Data is active! Starting training.")
        else:
            print("No data detected, but continuing anyway. Training will wait for data.")

        # Run the training loop with progress monitoring
        print("\n" + "="*50)
        print("STARTING TRAINING LOOP")
        print("="*50)

        # Add a monitoring thread to show that the program is still running
        def monitor_data_activity():
            last_report = time.time()
            while True:
                current_time = time.time()
                if current_time - last_report > 10:  # Report every 10 seconds
                    print(f"[Monitor] Data active: {env.data_active}, Last data received: {time.time() - env.last_data_received_time:.1f}s ago")
                    last_report = current_time
                time.sleep(1)

        import threading
        monitor_thread = threading.Thread(target=monitor_data_activity, daemon=True)
        monitor_thread.start()

        # Run the training loop
        returns = trainer.run_training()

        # Save the final model
        final_model_path = save_model(agent, env, iteration="final",
                                    best_params=best_params,
                                    returns=returns)

        if final_model_path:
            print(f"Final model saved to {final_model_path}")
            logging.info(f"Final model saved to {final_model_path}")

        # Print training results
        if returns and len(returns) > 0:
            print(f"Training completed with final return: {returns[-1]}")
            print(f"Best return during training: {max(returns)}")
            logging.info(f"Training completed with final return: {returns[-1]}")
            logging.info(f"Best return during training: {max(returns)}")
            # Save returns to file for visualization
            with open("best_agent_returns.json", "w") as f:
                json.dump(returns, f)

            print("Returns saved to best_agent_returns.json")
            logging.info("Returns saved to best_agent_returns.json")
            # Create a simple plot of returns
            try:
                import matplotlib.pyplot as plt

                # Create plots directory if it doesn't exist
                os.makedirs("plots", exist_ok=True)

                # Plot returns
                plt.figure(figsize=(10, 6))
                plt.plot(returns)
                plt.title("Returns during training")
                plt.xlabel("Evaluation step")
                plt.ylabel("Return")
                plt.grid(True)
                plt.savefig("plots/best_agent_returns.png")

                print("Returns plot saved to plots/best_agent_returns.png")
                logging.info("Returns plot saved to plots/best_agent_returns.png")
            except Exception as e:
                print(f"Error creating returns plot: {e}")
                logging.error(f"Error creating returns plot: {e}")
        else:
            print("Training completed but no returns were recorded")
            logging.warning("Training completed but no returns were recorded")

    except KeyboardInterrupt:
        print("\nTraining interrupted by user.")
        logging.info("Training interrupted by user.")
    except Exception as e:
        print(f"Error during training: {e}")
        logging.error(f"Error during training: {e}")
        import traceback
        traceback.print_exc()
        logging.error(traceback.format_exc())
        return 1
    finally:
        # Make sure to close the environment
        if env:
            print("Closing environment...")
            logging.info("Closing environment...")
            env.close()
            print("Environment closed.")
            logging.info("Environment closed.")

        # Clean up multiprocessing resources
        if 'multiprocessing' in sys.modules:
            try:
                import multiprocessing.pool
                multiprocessing.pool.ThreadPool._wrap_exception = True
            except:
                pass

    return 0

if __name__ == "__main__":
    sys.exit(main())
